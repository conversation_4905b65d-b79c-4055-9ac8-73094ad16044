#!/usr/bin/env python3
"""
Test script for Production Memory System
Demonstrates permanent user sessions and structured memory storage
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

from utils.production_memory_manager import get_production_memory_manager
from utils.structured_memory import (
    MemoryType, ImportanceLevel, KnowledgeTriple,
    create_user_profile, create_memory
)

load_dotenv()

def test_user_profile_management():
    """Test user profile creation and management"""
    
    print("👤 TESTING USER PROFILE MANAGEMENT")
    print("=" * 50)
    
    # Get memory manager
    memory_manager = get_production_memory_manager("ambition-guru", "gemini")
    
    # Test 1: Create new user profile
    print("\n1️⃣ Creating new user profile...")
    user_id = "test_user_123"
    profile = memory_manager.get_or_create_user_profile(user_id)
    print(f"✅ Created profile for {user_id}")
    print(f"   Profile completeness: {profile.profile_completeness:.1%}")
    
    # Test 2: Update user profile
    print("\n2️⃣ Updating user profile...")
    updates = {
        "name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "phone": "9841234567",
        "education_level": "Bachelor's",
        "course_interests": ["IELTS", "German Language"],
        "learning_goals": ["Study abroad in Germany", "Improve English skills"]
    }
    
    updated_profile = memory_manager.update_user_profile(user_id, updates)
    print(f"✅ Updated profile")
    print(f"   Name: {updated_profile.name}")
    print(f"   Email: {updated_profile.email}")
    print(f"   Course interests: {updated_profile.course_interests}")
    print(f"   Profile completeness: {updated_profile.profile_completeness:.1%}")
    
    return user_id, memory_manager

def test_structured_memory_storage():
    """Test structured memory storage and retrieval"""
    
    print("\n\n🧠 TESTING STRUCTURED MEMORY STORAGE")
    print("=" * 50)
    
    user_id, memory_manager = test_user_profile_management()
    
    # Test different types of memories
    memories_to_save = [
        {
            "content": "User wants to study IELTS for immigration to Canada",
            "memory_type": MemoryType.LEARNING_GOAL,
            "importance": ImportanceLevel.HIGH
        },
        {
            "content": "User prefers weekend classes due to work schedule",
            "memory_type": MemoryType.PREFERENCE,
            "importance": ImportanceLevel.MEDIUM
        },
        {
            "content": "User asked about IELTS exam fees and registration process",
            "memory_type": MemoryType.QUESTION,
            "importance": ImportanceLevel.LOW
        },
        {
            "content": "User completed SEE Bridge course with excellent results",
            "memory_type": MemoryType.BOOKING_HISTORY,
            "importance": ImportanceLevel.HIGH
        },
        {
            "content": "User gave positive feedback about teaching quality",
            "memory_type": MemoryType.FEEDBACK,
            "importance": ImportanceLevel.MEDIUM
        }
    ]
    
    print(f"\n📝 Saving {len(memories_to_save)} structured memories...")
    saved_memories = []
    
    for i, mem_data in enumerate(memories_to_save, 1):
        memory = memory_manager.save_structured_memory(
            user_id=user_id,
            content=mem_data["content"],
            memory_type=mem_data["memory_type"],
            importance=mem_data["importance"],
            context=f"Test conversation {i}"
        )
        saved_memories.append(memory)
        print(f"   {i}. [{mem_data['memory_type'].value}] {mem_data['content'][:50]}...")
    
    print(f"✅ Saved {len(saved_memories)} memories")
    return user_id, memory_manager, saved_memories

def test_memory_search():
    """Test memory search functionality"""
    
    print("\n\n🔍 TESTING MEMORY SEARCH")
    print("=" * 50)
    
    user_id, memory_manager, saved_memories = test_structured_memory_storage()
    
    # Test different search queries
    search_queries = [
        {
            "query": "IELTS course information",
            "description": "Search for IELTS-related memories"
        },
        {
            "query": "weekend classes schedule",
            "description": "Search for scheduling preferences"
        },
        {
            "query": "previous course completion",
            "description": "Search for booking history"
        },
        {
            "query": "feedback about teaching",
            "description": "Search for feedback memories"
        }
    ]
    
    for i, search_data in enumerate(search_queries, 1):
        print(f"\n{i}️⃣ {search_data['description']}")
        print(f"   Query: '{search_data['query']}'")
        
        results = memory_manager.search_memories(
            user_id=user_id,
            query=search_data["query"],
            limit=3
        )
        
        if results:
            print(f"   Found {len(results)} relevant memories:")
            for j, result in enumerate(results, 1):
                memory = result.memory
                print(f"     {j}. [{memory.memory_type.value}] {memory.content[:60]}...")
                print(f"        Relevance: {result.relevance_score:.2f}, Importance: {memory.importance.value}")
        else:
            print("   No relevant memories found")
    
    return user_id, memory_manager

def test_permanent_sessions():
    """Test permanent session management"""
    
    print("\n\n🔄 TESTING PERMANENT SESSIONS")
    print("=" * 50)
    
    user_id, memory_manager = test_memory_search()
    
    # Test session creation
    print("\n1️⃣ Creating permanent session...")
    session = memory_manager.get_permanent_session(user_id)
    print(f"✅ Created session: {session.session_id}")
    print(f"   Thread ID: {session.thread_id}")
    print(f"   User ID: {session.user_id}")
    print(f"   Session type: {session.session_type}")
    print(f"   Started at: {session.started_at}")
    
    # Test session retrieval (should get same session)
    print("\n2️⃣ Retrieving existing session...")
    same_session = memory_manager.get_permanent_session(user_id)
    print(f"✅ Retrieved session: {same_session.session_id}")
    print(f"   Same session ID: {session.session_id == same_session.session_id}")
    print(f"   Total messages: {same_session.total_messages}")
    
    return user_id, memory_manager

def test_user_context_generation():
    """Test comprehensive user context generation"""
    
    print("\n\n📋 TESTING USER CONTEXT GENERATION")
    print("=" * 50)
    
    user_id, memory_manager = test_permanent_sessions()
    
    # Test context generation with different queries
    test_queries = [
        "I want to book IELTS course",
        "What are my previous courses?",
        "Tell me about weekend schedules",
        "General conversation"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}️⃣ Context for query: '{query}'")
        print("-" * 40)
        
        context = memory_manager.get_user_context(user_id, query)
        print(context)
    
    return user_id, memory_manager

def test_memory_tools():
    """Test memory tools functionality"""
    
    print("\n\n🛠️ TESTING MEMORY TOOLS")
    print("=" * 50)
    
    user_id, memory_manager = test_user_context_generation()
    
    # Create memory tools
    tools = memory_manager.create_memory_tools()
    print(f"✅ Created {len(tools)} memory tools:")
    for tool in tools:
        print(f"   - {tool.name}: {tool.description}")
    
    # Test tools with mock config
    from langchain_core.runnables import RunnableConfig
    config = RunnableConfig(configurable={"user_id": user_id})
    
    print(f"\n📝 Testing save_user_memory tool...")
    save_tool = tools[0]  # save_user_memory
    result = save_tool.invoke({
        "content": "User is interested in Korean language course for K-pop culture",
        "memory_type": "course_interest",
        "importance": "medium",
        "config": config
    })
    print(f"   Result: {result}")
    
    print(f"\n🔍 Testing search_user_memories tool...")
    search_tool = tools[1]  # search_user_memories
    result = search_tool.invoke({
        "query": "Korean language",
        "limit": 3,
        "config": config
    })
    print(f"   Result: {result}")
    
    print(f"\n👤 Testing update_user_profile tool...")
    update_tool = tools[2]  # update_user_profile
    result = update_tool.invoke({
        "field": "course_interests",
        "value": "Korean Language",
        "config": config
    })
    print(f"   Result: {result}")

def show_production_benefits():
    """Show benefits of the production memory system"""
    
    print("\n\n🚀 PRODUCTION MEMORY SYSTEM BENEFITS")
    print("=" * 50)
    
    benefits = [
        "✅ Permanent user sessions - no conversation resets",
        "✅ Structured data storage with Pydantic models",
        "✅ Semantic memory search with embeddings",
        "✅ Multiple memory types (personal_info, course_interest, etc.)",
        "✅ Importance levels for memory prioritization",
        "✅ Knowledge triples for structured knowledge",
        "✅ MongoDB-based persistence with indexes",
        "✅ LLM-agnostic design (Gemini, OpenAI, etc.)",
        "✅ Profile completeness tracking",
        "✅ Memory access statistics",
        "✅ Cross-thread memory sharing",
        "✅ Production-ready error handling",
        "✅ Comprehensive user context generation",
        "✅ Tool-based memory management for agents"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print(f"\n🎯 USE CASES:")
    use_cases = [
        "• Educational course booking systems",
        "• Customer service with long-term relationships",
        "• Personalized learning platforms",
        "• Multi-session consultation services",
        "• E-commerce with customer preferences",
        "• Healthcare patient management",
        "• Financial advisory services"
    ]
    
    for use_case in use_cases:
        print(f"  {use_case}")

if __name__ == "__main__":
    print("🧪 PRODUCTION MEMORY SYSTEM TEST")
    print("=" * 60)
    
    try:
        test_user_profile_management()
        test_structured_memory_storage()
        test_memory_search()
        test_permanent_sessions()
        test_user_context_generation()
        test_memory_tools()
        show_production_benefits()
        
        print("\n\n✅ ALL TESTS COMPLETED SUCCESSFULLY!")
        print("The production memory system is ready for deployment.")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        print("Make sure MongoDB is running and environment variables are set.")
    
    print("\n🔧 NEXT STEPS:")
    print("1. Deploy to production environment")
    print("2. Configure MongoDB indexes for performance")
    print("3. Set up monitoring and logging")
    print("4. Implement memory cleanup policies")
    print("5. Add admin tools for memory management")
