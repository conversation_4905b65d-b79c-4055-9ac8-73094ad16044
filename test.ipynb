{"cells": [{"cell_type": "code", "execution_count": null, "id": "d5531a4f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "18e20c47", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "test_agent", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}