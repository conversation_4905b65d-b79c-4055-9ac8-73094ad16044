/**
 * Chat Hook
 * Manages chat state and provides chat functionality
 */

import { useState, useCallback, useEffect } from 'react';
import { chatService } from '../services';
import type { ChatMessage } from '../services';

interface UseChatReturn {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string) => Promise<void>;
  clearHistory: () => Promise<void>;
  isTyping: boolean;
}

export const useChat = (): UseChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);

  // Load chat history on mount
  useEffect(() => {
    const history = chatService.getChatHistory();
    setMessages(history);
  }, []);

  const sendMessage = useCallback(async (message: string): Promise<void> => {
    if (!message.trim()) return;

    setIsLoading(true);
    setError(null);
    setIsTyping(true);

    try {
      // Add user message immediately
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        message,
        response: '',
        timestamp: new Date(),
        thread_id: '',
        user_id: '',
      };

      setMessages(prev => [...prev, userMessage]);

      // Send to API
      const response = await chatService.sendMessage(message);

      // Update with response
      setMessages(prev => {
        const updated = [...prev];
        const lastMessage = updated[updated.length - 1];
        if (lastMessage) {
          lastMessage.response = response.response;
          lastMessage.thread_id = response.thread_id;
          lastMessage.user_id = response.user_id;
          lastMessage.tools_used = response.tools_used || [];
        }
        return updated;
      });

    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
      
      // Remove the failed message
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  }, []);

  const clearHistory = useCallback(async (): Promise<void> => {
    try {
      await chatService.clearHistory();
      setMessages([]);
      setError(null);
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to clear conversation');
    }
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearHistory,
    isTyping,
  };
};
