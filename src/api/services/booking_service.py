import logging
from datetime import datetime
from typing import List, Optional
from bson import ObjectId
from pymongo.database import Database

from models.booking import BookingModel, BookingCreate, BookingResponse
from core.database import get_db_from_tenant_id

logger = logging.getLogger(__name__)


class BookingService:
    """Service for managing course bookings"""
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.collection = self.db.bookings
        
    async def create_booking(
        self, 
        booking_data: BookingCreate, 
        user_id: str, 
        thread_id: str
    ) -> BookingResponse:
        """Create a new booking"""
        try:
            # Create booking document
            booking_doc = {
                "user_name": booking_data.user_name,
                "user_email": booking_data.user_email,
                "user_phone": booking_data.user_phone,
                "course_name": booking_data.course_name,
                "course_code": booking_data.course_code,
                "time_slot": booking_data.time_slot,
                "booking_date": datetime.now(),
                "status": booking_data.status,
                "tenant_id": self.tenant_id,
                "user_id": user_id,
                "thread_id": thread_id
            }
            
            # Insert into database
            result = self.collection.insert_one(booking_doc)
            booking_id = str(result.inserted_id)
            
            logger.info(f"✅ Booking created with ID: {booking_id}")
            
            return BookingResponse(
                booking_id=booking_id,
                user_name=booking_data.user_name,
                course_name=booking_data.course_name,
                course_code=booking_data.course_code,
                time_slot=booking_data.time_slot,
                booking_date=booking_doc["booking_date"],
                status=booking_data.status
            )
            
        except Exception as e:
            logger.error(f"❌ Error creating booking: {e}")
            raise
    
    async def get_user_bookings(self, user_id: str) -> List[BookingResponse]:
        """Get all bookings for a user"""
        try:
            bookings = list(self.collection.find({"user_id": user_id}))
            
            return [
                BookingResponse(
                    booking_id=str(booking["_id"]),
                    user_name=booking["user_name"],
                    course_name=booking["course_name"],
                    course_code=booking["course_code"],
                    time_slot=booking["time_slot"],
                    booking_date=booking["booking_date"],
                    status=booking["status"]
                )
                for booking in bookings
            ]
            
        except Exception as e:
            logger.error(f"❌ Error getting user bookings: {e}")
            return []
    
    async def get_booking_by_id(self, booking_id: str) -> Optional[BookingResponse]:
        """Get a specific booking by ID"""
        try:
            booking = self.collection.find_one({"_id": ObjectId(booking_id)})
            
            if booking:
                return BookingResponse(
                    booking_id=str(booking["_id"]),
                    user_name=booking["user_name"],
                    course_name=booking["course_name"],
                    course_code=booking["course_code"],
                    time_slot=booking["time_slot"],
                    booking_date=booking["booking_date"],
                    status=booking["status"]
                )
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting booking: {e}")
            return None
    
    async def cancel_booking(self, booking_id: str, user_id: str) -> bool:
        """Cancel a booking"""
        try:
            result = self.collection.update_one(
                {"_id": ObjectId(booking_id), "user_id": user_id},
                {"$set": {"status": "cancelled"}}
            )
            
            if result.modified_count > 0:
                logger.info(f"✅ Booking {booking_id} cancelled")
                return True
            return False
            
        except Exception as e:
            logger.error(f"❌ Error cancelling booking: {e}")
            return False
