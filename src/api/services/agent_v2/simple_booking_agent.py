"""
Simple Booking Agent - Clean booking agent for ChatService
"""

import logging
from typing import List, Dict
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.mongodb import MongoDBSaver
from models.user import UserTenantDB
from core.database import get_db_from_tenant_id
from api.services.booking_service import BookingService
from utils.memory_system import get_memory_system
import os
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Note: Using shared MongoDB memory instead of separate sessions


class SimpleBookingAgent:
    """
    Simple Booking Agent - Clean booking workflow
    """
    
    def __init__(self, current_user : UserTenantDB=None):
        """Initialize the booking agent"""
        self.current_user = current_user

        # LLM for context understanding
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )

        # Access to search tools through current user
        self.vector_store_manager = current_user.vector_store_manager if current_user else None

        # Initialize booking service for database operations
        if current_user and current_user.tenant_id:
            self.booking_service = BookingService(current_user.tenant_id)
        else:
            self.booking_service = None

        # Initialize memory system for personalization
        if current_user and current_user.tenant_id:
            self.memory_system = get_memory_system(current_user.tenant_id, "gemini")
        else:
            self.memory_system = None

        # Use the same MongoDB memory as main agent
        if current_user and current_user.tenant_id:
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)
            mongo_client = tenant_db.client
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )
            logger.info(f"✅ Booking agent using shared MongoDB memory for tenant: {current_user.tenant_id}")
        else:
            self.memory = None
            logger.warning("⚠️ No memory available for booking agent")

        logger.info("✅ Simple Booking Agent initialized")

    def handle_booking_request(self, user_message: str, thread_id: str) -> str:
        """Handle booking request"""
        try:
            logger.info(f"📅 Booking request: {user_message}")

            # Get conversation context from shared memory
            conversation_context = self._get_conversation_context(thread_id)
            logger.info(f"🔍 Booking agent conversation context available")

            # Check if user already has a selected product
            selected_product = None
            search_results = ""

            if self.memory_system:
                selected_product = self.memory_system.get_selected_product(thread_id)

            # Only search for products if no product is selected yet
            if not selected_product and self.vector_store_manager:
                try:
                    # Get the raw search documents first
                    retriever = self.vector_store_manager.get_product_retriever()
                    if retriever:
                        search_docs = retriever.invoke(user_message)

                        # Check if user is selecting from the search results
                        self._check_for_course_selection(user_message, search_docs, thread_id)

                        # Now get the formatted search results
                        search_results = self.vector_store_manager.search_products(user_message)
                        logger.info(f"🔍 Found course search results for booking request")
                    else:
                        search_results = "No courses available at the moment."

                except Exception as e:
                    logger.warning(f"⚠️ Could not search for courses: {e}")
            elif selected_product:
                logger.info(f"✅ Using previously selected product: {selected_product['name']} ({selected_product['code']})")
                search_results = f"Selected Course: {selected_product['name']} (Code: {selected_product['code']})"

            # Update student profile and get personalized context using LLM
            personalized_context = "No personalization available."
            if self.memory_system:
                try:
                    # Get full conversation history from MongoDB memory
                    conversation_messages = self._get_conversation_messages(thread_id)

                    # Add current message
                    conversation_messages.append({"role": "user", "content": user_message})

                    # Update student profile using LLM with full conversation history
                    self.memory_system.update_student_profile(thread_id, conversation_messages)

                    # Get personalized context using LLM
                    personalized_context = self.memory_system.get_personalized_context(thread_id, user_message)

                    logger.info(f"✅ Updated student profile and got personalized context")
                except Exception as e:
                    logger.warning(f"⚠️ Could not update memory system: {e}")



            # Use LLM to understand the booking request with search context
            if selected_product:
                system_prompt = """You are a helpful booking assistant for an educational service center in Nepal.

The user has already selected a course. Help them complete the booking by:
1. Confirming their selected course details
2. IMPORTANT: Check the Student Profile first - if name, email, and phone are already available, DO NOT ask for them again. Only ask for missing information.
3. Offering available time slots
4. Finalizing the booking

GREETING BEHAVIOR:
🆕 FOR NEW CUSTOMERS (when profile shows "NEW CUSTOMER"):
- Welcome them warmly to the booking process
- Briefly explain what information you'll need
- Be encouraging about their course choice

🔄 FOR RETURNING CUSTOMERS (when profile shows customer name):
- Use their name in greeting
- Reference their selected course
- Continue the booking process efficiently

Be friendly and guide them through the process step by step.
Use "Namaste" as greeting when appropriate.

CRITICAL RULE: NEVER ask for information that is already provided in the Student Profile. If the profile shows the user's name, email, and phone number, use that information directly and proceed to scheduling.

Student Profile:
{personalized_context}

Selected Course:
{search_context}

Conversation History Context:
{conversation_context}

Focus on completing the booking for their selected course. If all user information is available in the profile, proceed directly to scheduling."""
            else:
                system_prompt = """You are a helpful booking assistant for an educational service center in Nepal.

Help users book courses by:
1. Understanding what course they want to book (use search results if available)
2. Using the student profile to provide personalized recommendations
3. Once they select a course, remember their choice and proceed with booking
4. IMPORTANT: Check the Student Profile first - if name, email, and phone are already available, DO NOT ask for them again. Only ask for missing information.
5. Offering available time slots
6. Confirming the booking and saving it to database

GREETING BEHAVIOR:
🆕 FOR NEW CUSTOMERS (when profile shows "NEW CUSTOMER"):
- Welcome them warmly to the booking process
- Ask what course they'd like to book
- Be encouraging and helpful

🔄 FOR RETURNING CUSTOMERS (when profile shows customer name):
- Use their name in greeting
- Reference their previous interests if available
- Help them select from available courses

Be friendly and guide them through the process step by step.
Use "Namaste" as greeting when appropriate.

CRITICAL RULE: NEVER ask for information that is already provided in the Student Profile. If the profile shows the user's name, email, and phone number, use that information directly.

Student Profile:
{personalized_context}

Available Courses:
{search_context}

Conversation History Context:
{conversation_context}

Respond helpfully to their request with personalized recommendations based on their profile."""

            search_context = f"Available courses from search:\n{search_results}\n" if search_results else "No specific course search results available."

            response = self.llm.invoke([
                SystemMessage(content=system_prompt.format(
                    personalized_context=personalized_context,
                    search_context=search_context,
                    conversation_context=conversation_context
                )),
                HumanMessage(content=f"User message: {user_message}")
            ])

            # Product selection is handled in _check_for_course_selection above

            result = response.content
            logger.info(f"✅ Booking response generated")
            return result
            
        except Exception as e:
            error_msg = f"Error handling booking: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble processing your booking request. Please try again or contact our support team."

    def _get_conversation_context(self, thread_id: str) -> str:
        """Get conversation context from shared MongoDB memory"""
        if not self.memory:
            return "No conversation history available."

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Return recent conversation context for the agent to use
                context_info = []
                for msg in messages[-10:]:  # Last 10 messages for context
                    if hasattr(msg, 'content'):
                        context_info.append(f"Message: {msg.content}")

                return "\n".join(context_info) if context_info else "No conversation history found."
            else:
                return "No conversation history found."

        except Exception as e:
            logger.warning(f"Could not retrieve conversation context: {e}")
            return "Error retrieving conversation history."

    def _get_conversation_messages(self, thread_id: str) -> List[Dict[str, str]]:
        """Get conversation messages in format suitable for memory system"""
        if not self.memory:
            return []

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Convert to memory system format
                conversation_messages = []
                for msg in messages[-20:]:  # Last 20 messages for better context
                    if hasattr(msg, 'content') and msg.content:
                        # Determine role based on message type
                        if hasattr(msg, 'type'):
                            if msg.type == 'human':
                                role = 'user'
                            elif msg.type == 'ai':
                                role = 'assistant'
                            else:
                                role = 'user'  # Default to user
                        else:
                            role = 'user'  # Default to user

                        conversation_messages.append({
                            "role": role,
                            "content": msg.content
                        })

                return conversation_messages
            else:
                return []

        except Exception as e:
            logger.warning(f"Could not retrieve conversation messages: {e}")
            return []

    def _check_for_course_selection(self, user_message: str, search_results: str, thread_id: str) -> None:
        """Use LLM to intelligently detect course selection from context"""
        try:
            # Use LLM to understand if user is selecting a course
            selection_prompt = f"""
You are analyzing a conversation where a user is selecting a course from available options.

User message: "{user_message}"

Available courses from previous search:
{search_results}

If the user is clearly selecting or confirming a specific course, extract the course name and code.
Respond in this exact format:
SELECTED: [Course Name] | [Course Code]

If no clear selection is made, respond:
NO_SELECTION

Examples:
- "Yes SEE Bridge Course" → SELECTED: SEE Bridge Course | SEE-BRIDGE
- "I want the IELTS course" → SELECTED: IELTS Preparation | IELTS-PREP
- "Book the first one" → SELECTED: [First course from list] | [Its code]
- "What other options do you have?" → NO_SELECTION
"""

            response = self.llm.invoke([
                SystemMessage(content="You are a course selection detector. Extract course selections from user messages."),
                HumanMessage(content=selection_prompt)
            ])

            response_text = response.content.strip()

            if response_text.startswith("SELECTED:"):
                # Parse the selection
                selection_part = response_text.replace("SELECTED:", "").strip()
                if "|" in selection_part:
                    course_name, course_code = selection_part.split("|", 1)
                    course_name = course_name.strip()
                    course_code = course_code.strip()

                    # Save the selected product
                    self.memory_system.save_selected_product(thread_id, course_name, course_code)
                    logger.info(f"🎯 User selected course: {course_name} ({course_code})")

        except Exception as e:
            logger.warning(f"⚠️ Could not check course selection: {e}")

    def get_pending_booking_reminder(self, _thread_id: str) -> str:
        """Get reminder message for pending booking"""
        # Since we're using shared memory, no separate booking reminders needed
        return ""
