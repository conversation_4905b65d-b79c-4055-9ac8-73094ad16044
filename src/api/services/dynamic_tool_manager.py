"""
Dynamic Tool Manager - Manages tool availability based on tenant configuration
Allows different tenants to have different sets of tools available
"""

import logging
from typing import List, Dict, Any, Optional
from langchain_core.tools import tool
from models.user import UserTenantDB
from utils import log_tool_call, log_tool_result

logger = logging.getLogger(__name__)


class ToolConfig:
    """Configuration for tool availability"""
    
    def __init__(self, 
                 search_information: bool = True,
                 search_products: bool = True, 
                 handle_booking: bool = True,
                 custom_tools: List[str] = None):
        """
        Initialize tool configuration
        
        Args:
            search_information: Enable information search tool
            search_products: Enable product search tool
            handle_booking: Enable booking tool
            custom_tools: List of custom tool names to enable
        """
        self.search_information = search_information
        self.search_products = search_products
        self.handle_booking = handle_booking
        self.custom_tools = custom_tools or []


class DynamicToolManager:
    """Manages dynamic tool creation based on configuration"""
    
    def __init__(self, current_user: UserTenantDB, tool_config: ToolConfig = None):
        """
        Initialize dynamic tool manager
        
        Args:
            current_user: Current user with tenant info
            tool_config: Configuration for which tools to enable
        """
        self.current_user = current_user
        self.tenant_id = current_user.tenant_id
        self.vector_store_manager = current_user.vector_store_manager
        
        # Use default config if none provided
        self.tool_config = tool_config or ToolConfig()
        
        logger.info(f"✅ Dynamic Tool Manager initialized for tenant: {self.tenant_id}")
        logger.info(f"🔧 Tool config: info={self.tool_config.search_information}, "
                   f"products={self.tool_config.search_products}, "
                   f"booking={self.tool_config.handle_booking}")
    
    def create_tools(self) -> List:
        """Create tools based on configuration"""
        tools = []

        # Add search information tool if enabled
        if self.tool_config.search_information:
            tools.append(self._create_search_information_tool())
            logger.info("✅ Added search_information tool")

        # Add search products tool if enabled
        if self.tool_config.search_products:
            tools.append(self._create_search_products_tool())
            logger.info("✅ Added search_products tool")

        # Add booking tool if enabled
        if self.tool_config.handle_booking:
            tools.append(self._create_booking_tool())
            logger.info("✅ Added handle_booking tool")

        # Add long-term memory tools (always enabled for persistent sessions)
        memory_tools = self._create_memory_tools()
        tools.extend(memory_tools)
        logger.info(f"✅ Added {len(memory_tools)} memory tools")

        # Add custom tools if any
        for custom_tool_name in self.tool_config.custom_tools:
            custom_tool = self._create_custom_tool(custom_tool_name)
            if custom_tool:
                tools.append(custom_tool)
                logger.info(f"✅ Added custom tool: {custom_tool_name}")

        logger.info(f"🛠️ Created {len(tools)} tools for tenant {self.tenant_id}")
        return tools
    
    def _create_search_information_tool(self):
        """Create search information tool"""
        
        @tool
        def search_information(user_message: str) -> str:
            """
            Search for general information, troubleshooting, and help content.
            
            Args:
                user_message: The user's exact message for information search
                
            Returns:
                Search results for general information
            """
            log_tool_call("search_information", f"user_message='{user_message}'")
            try:
                result = self.vector_store_manager.search_information(user_message)
                log_tool_result(result)
                return result
            except Exception as e:
                error_msg = f"Error in information search: {str(e)}"
                log_tool_result(error_msg)
                return error_msg
        
        return search_information
    
    def _create_search_products_tool(self):
        """Create search products tool"""
        
        @tool
        def search_products(user_message: str) -> str:
            """
            Search for products, courses, and educational programs.
            
            Args:
                user_message: The user's exact message for product search
                
            Returns:
                Search results for courses/products
            """
            log_tool_call("search_products", f"user_message='{user_message}'")
            try:
                result = self.vector_store_manager.search_products(user_message)
                log_tool_result(result)
                return result
            except Exception as e:
                error_msg = f"Error in product search: {str(e)}"
                log_tool_result(error_msg)
                return error_msg
        
        return search_products
    
    def _create_booking_tool(self):
        """Create booking tool"""
        
        @tool
        def handle_booking(user_message: str) -> str:
            """
            Handle booking requests and course enrollment.
            
            Args:
                user_message: The user's exact message about booking
                
            Returns:
                Booking response and next steps
            """
            log_tool_call("handle_booking", f"user_message='{user_message}'")
            try:
                # Import here to avoid circular imports
                from api.services.agent_v2.simple_booking_agent import SimpleBookingAgent
                
                booking_agent = SimpleBookingAgent(current_user=self.current_user)
                thread_id = str(self.current_user.user.id)
                result = booking_agent.handle_booking_request(user_message, thread_id)
                log_tool_result(result)
                return result
            except Exception as e:
                error_msg = f"Error handling booking: {str(e)}"
                log_tool_result(error_msg)
                return error_msg
        
        return handle_booking

    def _create_memory_tools(self):
        """Create production memory tools"""
        try:
            from utils.production_memory_manager import get_production_memory_manager

            # Get production memory manager for this tenant
            memory_manager = get_production_memory_manager(self.current_user.tenant_id, "gemini")

            # Create memory tools
            memory_tools = memory_manager.create_memory_tools()
            return memory_tools

        except Exception as e:
            logger.warning(f"Could not create memory tools: {e}")
            return []

    def _create_custom_tool(self, tool_name: str):
        """Create custom tool based on name"""
        # This can be extended to support custom tools
        # For now, return None for unknown tools
        logger.warning(f"Custom tool '{tool_name}' not implemented")
        return None
    
    def get_tool_descriptions(self) -> Dict[str, str]:
        """Get descriptions of available tools"""
        descriptions = {}
        
        if self.tool_config.search_information:
            descriptions["search_information"] = "Search for general information and troubleshooting"
        
        if self.tool_config.search_products:
            descriptions["search_products"] = "Search for courses and educational programs"
        
        if self.tool_config.handle_booking:
            descriptions["handle_booking"] = "Handle booking requests and course enrollment"
        
        return descriptions
    
    def update_config(self, new_config: ToolConfig):
        """Update tool configuration"""
        self.tool_config = new_config
        logger.info(f"🔄 Updated tool config for tenant {self.tenant_id}")
    
    @classmethod
    def create_config_from_dict(cls, config_dict: Dict[str, Any]) -> ToolConfig:
        """Create ToolConfig from dictionary"""
        return ToolConfig(
            search_information=config_dict.get("search_information", True),
            search_products=config_dict.get("search_products", True),
            handle_booking=config_dict.get("handle_booking", True),
            custom_tools=config_dict.get("custom_tools", [])
        )
    
    @classmethod
    def get_default_config(cls) -> ToolConfig:
        """Get default tool configuration"""
        return ToolConfig()
    
    @classmethod
    def get_search_only_config(cls) -> ToolConfig:
        """Get configuration with only search tools"""
        return ToolConfig(
            search_information=True,
            search_products=True,
            handle_booking=False
        )
    
    @classmethod
    def get_info_only_config(cls) -> ToolConfig:
        """Get configuration with only information search"""
        return ToolConfig(
            search_information=True,
            search_products=False,
            handle_booking=False
        )
    
    @classmethod
    def get_products_only_config(cls) -> ToolConfig:
        """Get configuration with only product search"""
        return ToolConfig(
            search_information=False,
            search_products=True,
            handle_booking=False
        )


def create_dynamic_tool_manager(current_user: UserTenantDB, 
                              config_type: str = "default") -> DynamicToolManager:
    """
    Factory function to create dynamic tool manager with predefined configurations
    
    Args:
        current_user: Current user with tenant info
        config_type: Type of configuration ("default", "search_only", "info_only", "products_only")
        
    Returns:
        DynamicToolManager instance
    """
    config_map = {
        "default": DynamicToolManager.get_default_config(),
        "search_only": DynamicToolManager.get_search_only_config(),
        "info_only": DynamicToolManager.get_info_only_config(),
        "products_only": DynamicToolManager.get_products_only_config()
    }
    
    config = config_map.get(config_type, DynamicToolManager.get_default_config())
    return DynamicToolManager(current_user, config)
