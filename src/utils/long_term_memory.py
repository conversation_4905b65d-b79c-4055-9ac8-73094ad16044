"""
Long-Term Memory System for Course Booking Agent
Implements persistent user sessions and memory using MongoDB and vector storage
Based on LangChain's long-term memory patterns
"""

import logging
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.documents import Document
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig
from langchain_community.vectorstores import FAISS
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from core.database import get_db_from_tenant_id
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


class UserMemory(BaseModel):
    """User memory structure for long-term storage"""
    user_id: str
    memory_content: str
    memory_type: str = "general"  # general, preference, fact, goal
    importance: float = 1.0  # 0.0 to 1.0
    created_at: datetime = Field(default_factory=datetime.now)
    last_accessed: datetime = Field(default_factory=datetime.now)
    access_count: int = 0


class LongTermMemorySystem:
    """Long-term memory system with persistent user sessions"""
    
    def __init__(self, tenant_id: str, llm_provider: str = "gemini"):
        """
        Initialize long-term memory system
        
        Args:
            tenant_id: Tenant identifier
            llm_provider: "gemini" or "openai"
        """
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.llm_provider = llm_provider
        
        # Initialize LLM
        if llm_provider == "gemini":
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-flash",
                temperature=0.1,
                google_api_key=os.getenv("GOOGLE_API_KEY")
            )
            # Initialize embeddings for vector storage
            self.embeddings = GoogleGenerativeAIEmbeddings(
                model="models/embedding-001",
                google_api_key=os.getenv("GOOGLE_API_KEY")
            )
        elif llm_provider == "openai":
            from langchain_openai import ChatOpenAI, OpenAIEmbeddings
            self.llm = ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0.1,
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
        else:
            raise ValueError("llm_provider must be 'gemini' or 'openai'")
        
        # Collections for memory storage
        self.memories_collection = self.db.user_memories
        self.sessions_collection = self.db.user_sessions
        
        # Initialize vector store for semantic memory search
        self._init_vector_store()
        
        logger.info(f"✅ Long-term memory system initialized with {llm_provider} for tenant: {tenant_id}")
    
    def _init_vector_store(self):
        """Initialize vector store for semantic memory retrieval"""
        try:
            # Try to load existing memories into vector store
            existing_memories = list(self.memories_collection.find())
            
            if existing_memories:
                documents = []
                for memory in existing_memories:
                    doc = Document(
                        page_content=memory['memory_content'],
                        metadata={
                            'user_id': memory['user_id'],
                            'memory_type': memory.get('memory_type', 'general'),
                            'importance': memory.get('importance', 1.0),
                            'created_at': memory.get('created_at', datetime.now()).isoformat(),
                            'memory_id': str(memory['_id'])
                        }
                    )
                    documents.append(doc)
                
                self.vector_store = FAISS.from_documents(documents, self.embeddings)
                logger.info(f"✅ Loaded {len(documents)} existing memories into vector store")
            else:
                # Create empty vector store
                self.vector_store = FAISS.from_texts([""], self.embeddings)
                logger.info("✅ Created empty vector store for memories")
                
        except Exception as e:
            logger.warning(f"Could not initialize vector store: {e}")
            # Fallback to empty vector store
            self.vector_store = FAISS.from_texts([""], self.embeddings)
    
    def get_user_id(self, config: RunnableConfig) -> str:
        """Extract user ID from config"""
        user_id = config["configurable"].get("user_id")
        if user_id is None:
            raise ValueError("User ID needs to be provided for memory operations.")
        return user_id
    
    def save_memory(self, user_id: str, memory_content: str, memory_type: str = "general", importance: float = 1.0) -> str:
        """Save memory to both MongoDB and vector store"""
        try:
            # Create memory document
            memory_doc = {
                'user_id': user_id,
                'memory_content': memory_content,
                'memory_type': memory_type,
                'importance': importance,
                'created_at': datetime.now(),
                'last_accessed': datetime.now(),
                'access_count': 0
            }
            
            # Save to MongoDB
            result = self.memories_collection.insert_one(memory_doc)
            memory_id = str(result.inserted_id)
            
            # Add to vector store
            doc = Document(
                page_content=memory_content,
                metadata={
                    'user_id': user_id,
                    'memory_type': memory_type,
                    'importance': importance,
                    'created_at': datetime.now().isoformat(),
                    'memory_id': memory_id
                }
            )
            
            self.vector_store.add_documents([doc])
            
            logger.info(f"✅ Saved memory for user {user_id}: {memory_content[:50]}...")
            return memory_content
            
        except Exception as e:
            logger.error(f"Error saving memory: {e}")
            return f"Error saving memory: {str(e)}"
    
    def search_memories(self, user_id: str, query: str, k: int = 3) -> List[str]:
        """Search for relevant memories using semantic similarity"""
        try:
            def _filter_function(doc: Document) -> bool:
                return doc.metadata.get("user_id") == user_id
            
            # Search vector store
            documents = self.vector_store.similarity_search(
                query, k=k, filter=_filter_function
            )
            
            # Update access count and last accessed time
            memory_ids = [doc.metadata.get('memory_id') for doc in documents if doc.metadata.get('memory_id')]
            if memory_ids:
                self.memories_collection.update_many(
                    {'_id': {'$in': [self._to_object_id(mid) for mid in memory_ids]}},
                    {
                        '$inc': {'access_count': 1},
                        '$set': {'last_accessed': datetime.now()}
                    }
                )
            
            return [doc.page_content for doc in documents]
            
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return []
    
    def _to_object_id(self, memory_id: str):
        """Convert string to MongoDB ObjectId"""
        try:
            from bson import ObjectId
            return ObjectId(memory_id)
        except:
            return memory_id
    
    def get_user_session(self, user_id: str) -> Dict[str, Any]:
        """Get or create permanent user session"""
        try:
            session = self.sessions_collection.find_one({'user_id': user_id})
            
            if not session:
                # Create new permanent session
                session_doc = {
                    'user_id': user_id,
                    'session_id': f"permanent_{user_id}",
                    'created_at': datetime.now(),
                    'last_active': datetime.now(),
                    'conversation_count': 0,
                    'is_permanent': True
                }
                self.sessions_collection.insert_one(session_doc)
                session = session_doc
                logger.info(f"✅ Created permanent session for user {user_id}")
            else:
                # Update last active time
                self.sessions_collection.update_one(
                    {'user_id': user_id},
                    {
                        '$set': {'last_active': datetime.now()},
                        '$inc': {'conversation_count': 1}
                    }
                )
                logger.info(f"✅ Updated session for user {user_id}")
            
            return session
            
        except Exception as e:
            logger.error(f"Error managing user session: {e}")
            return {
                'user_id': user_id,
                'session_id': f"permanent_{user_id}",
                'created_at': datetime.now(),
                'last_active': datetime.now(),
                'conversation_count': 0,
                'is_permanent': True
            }
    
    def get_user_context(self, user_id: str, current_query: str = "") -> str:
        """Get comprehensive user context from long-term memory"""
        try:
            # Get user session info
            session = self.get_user_session(user_id)
            
            # Search for relevant memories
            relevant_memories = self.search_memories(user_id, current_query, k=5)
            
            # Build context
            context_parts = [
                f"USER SESSION: Permanent session for user {user_id}",
                f"Session created: {session.get('created_at', 'Unknown')}",
                f"Total conversations: {session.get('conversation_count', 0)}",
                f"Last active: {session.get('last_active', 'Unknown')}"
            ]
            
            if relevant_memories:
                context_parts.append("\nRELEVANT MEMORIES:")
                for i, memory in enumerate(relevant_memories, 1):
                    context_parts.append(f"{i}. {memory}")
            else:
                context_parts.append("\nNo relevant memories found for this user.")
            
            context_parts.append(
                "\nIMPORTANT: This user has a permanent session. "
                "All information should be remembered across conversations. "
                "Use the save_memory tool to store important information."
            )
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return f"Error retrieving user context for {user_id}"
    
    def create_memory_tools(self):
        """Create memory management tools for the agent"""
        
        @tool
        def save_user_memory(memory: str, memory_type: str = "general", importance: float = 1.0, config: RunnableConfig = None) -> str:
            """
            Save important information about the user for future conversations.
            
            Args:
                memory: The information to remember about the user
                memory_type: Type of memory (general, preference, fact, goal)
                importance: Importance level from 0.0 to 1.0
                config: Runtime configuration containing user_id
            """
            if config is None:
                return "Error: Configuration required for memory operations"
            
            user_id = self.get_user_id(config)
            return self.save_memory(user_id, memory, memory_type, importance)
        
        @tool
        def search_user_memories(query: str, config: RunnableConfig = None) -> List[str]:
            """
            Search for relevant memories about the user.
            
            Args:
                query: Search query to find relevant memories
                config: Runtime configuration containing user_id
            """
            if config is None:
                return ["Error: Configuration required for memory operations"]
            
            user_id = self.get_user_id(config)
            return self.search_memories(user_id, query)
        
        return [save_user_memory, search_user_memories]
    
    def clear_user_memories(self, user_id: str) -> bool:
        """Clear all memories for a specific user (for testing/admin purposes)"""
        try:
            # Remove from MongoDB
            result = self.memories_collection.delete_many({'user_id': user_id})
            
            # Reinitialize vector store (simple approach - could be optimized)
            self._init_vector_store()
            
            logger.info(f"✅ Cleared {result.deleted_count} memories for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing memories for user {user_id}: {e}")
            return False


# Global memory system instances
_memory_systems: Dict[str, LongTermMemorySystem] = {}


def get_long_term_memory_system(tenant_id: str, llm_provider: str = "gemini") -> LongTermMemorySystem:
    """Get or create long-term memory system for tenant"""
    key = f"{tenant_id}_{llm_provider}"
    if key not in _memory_systems:
        _memory_systems[key] = LongTermMemorySystem(tenant_id, llm_provider)
    return _memory_systems[key]
