"""
Memory System for Course Booking Agent
Implements user profiles and semantic memory using Gemini/OpenAI
Based on LangMem patterns but using simpler memory management
"""

import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from langchain_core.messages import SystemMessage, HumanMessage
from core.database import get_db_from_tenant_id
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


class StudentProfile(BaseModel):
    """Student profile structure for personalized interactions"""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    preferred_language: Optional[str] = None  # "Nepali", "English"
    education_level: Optional[str] = None     # "SEE", "+2", "Bachelor's"
    course_interests: List[str] = []          # ["IELTS", "German", "Korean"]
    learning_goals: Optional[str] = None
    budget_range: Optional[str] = None
    preferred_schedule: Optional[str] = None  # "Morning", "Evening", "Weekend"
    selected_product: Optional[Dict[str, str]] = None  # {"name": "SEE Bridge Course", "code": "SEE-BRIDGE"}
    last_updated: datetime = Field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    def is_complete(self) -> bool:
        """Check if basic profile information is complete"""
        return all([self.name, self.email, self.phone])

    def has_selected_product(self) -> bool:
        """Check if user has already selected a product"""
        return self.selected_product is not None and all([
            self.selected_product.get('name'),
            self.selected_product.get('code')
        ])


class CourseMemory(BaseModel):
    """Course-related knowledge and insights"""
    course_type: str                    # "SEE Bridge", "BBS", "CSIT", "IELTS"
    common_questions: List[str] = []    # Frequently asked questions
    success_stories: List[str] = []     # Student testimonials
    prerequisites: List[str] = []       # Required background
    difficulty_insights: Optional[str] = None
    enrollment_patterns: List[str] = [] # When students typically enroll
    last_updated: datetime = Field(default_factory=datetime.now)


class MemorySystem:
    """Memory system for course booking agent using Gemini/OpenAI"""
    
    def __init__(self, tenant_id: str, llm_provider: str = "gemini"):
        """
        Initialize memory system
        
        Args:
            tenant_id: Tenant identifier
            llm_provider: "gemini" or "openai"
        """
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.llm_provider = llm_provider
        
        # Initialize LLM based on provider
        if llm_provider == "gemini":
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-flash",
                temperature=0.1,
                google_api_key=os.getenv("GOOGLE_API_KEY")
            )
        elif llm_provider == "openai":
            from langchain_openai import ChatOpenAI
            self.llm = ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0.1,
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
        else:
            raise ValueError("llm_provider must be 'gemini' or 'openai'")
        
        # Collections for memory storage
        self.profiles_collection = self.db.student_profiles
        self.course_memory_collection = self.db.course_memory
        
        logger.info(f"✅ Memory system initialized with {llm_provider} for tenant: {tenant_id}")
    
    def get_student_profile(self, user_id: str) -> Optional[StudentProfile]:
        """Get student profile from memory"""
        try:
            profile_doc = self.profiles_collection.find_one({"user_id": user_id})
            if profile_doc:
                # Remove MongoDB _id field
                profile_doc.pop('_id', None)
                profile_doc.pop('user_id', None)
                return StudentProfile(**profile_doc)
            return None
        except Exception as e:
            logger.error(f"Error retrieving student profile: {e}")
            return None
    
    def update_student_profile(self, user_id: str, conversation_messages: List[Dict[str, str]]) -> StudentProfile:
        """Update student profile based on conversation using LLM"""
        try:
            # Get existing profile
            existing_profile = self.get_student_profile(user_id)
            
            # Prepare conversation text for analysis
            conversation_text = "\n".join([
                f"{msg.get('role', 'user')}: {msg.get('content', '')}" 
                for msg in conversation_messages[-10:]  # Last 10 messages
            ])
            
            # Create system prompt for profile extraction
            system_prompt = f"""You are an expert at extracting student information from conversations.

Analyze the conversation and extract/update the following student profile information:
- name: Student's full name
- email: Email address
- phone: Phone number  
- preferred_language: "Nepali" or "English"
- education_level: "SEE", "+2", "Bachelor's", etc.
- course_interests: List of courses mentioned (IELTS, German, Korean, SEE Bridge, BBS, CSIT, etc.)
- learning_goals: What the student wants to achieve
- budget_range: Any budget mentions
- preferred_schedule: "Morning", "Evening", "Weekend"

Current profile: {existing_profile.to_dict() if existing_profile else "No existing profile"}

Conversation:
{conversation_text}

Return ONLY a JSON object with the updated profile information. Only include fields that are mentioned or can be inferred from the conversation. Do not make up information."""

            # Use LLM to extract profile information
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content="Extract and update the student profile from this conversation.")
            ]
            
            response = self.llm.invoke(messages)
            
            # Parse LLM response
            try:
                # Extract JSON from response
                response_text = response.content.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:-3]
                elif response_text.startswith('```'):
                    response_text = response_text[3:-3]
                
                profile_updates = json.loads(response_text)
                
                # Merge with existing profile
                if existing_profile:
                    profile_data = existing_profile.to_dict()
                    profile_data.update(profile_updates)
                else:
                    profile_data = profile_updates
                
                # Ensure course_interests is a list
                if 'course_interests' in profile_data and isinstance(profile_data['course_interests'], str):
                    profile_data['course_interests'] = [profile_data['course_interests']]
                
                profile_data['last_updated'] = datetime.now()
                updated_profile = StudentProfile(**profile_data)
                
                # Save to database
                self.profiles_collection.update_one(
                    {"user_id": user_id},
                    {"$set": {**updated_profile.to_dict(), "user_id": user_id}},
                    upsert=True
                )
                
                logger.info(f"✅ Updated student profile for user {user_id}")
                return updated_profile
                
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing LLM response: {e}")
                return existing_profile or StudentProfile()
                
        except Exception as e:
            logger.error(f"Error updating student profile: {e}")
            return existing_profile or StudentProfile()
    
    def get_course_memory(self, course_type: str) -> Optional[CourseMemory]:
        """Get course-related memory"""
        try:
            memory_doc = self.course_memory_collection.find_one({"course_type": course_type})
            if memory_doc:
                memory_doc.pop('_id', None)
                return CourseMemory(**memory_doc)
            return None
        except Exception as e:
            logger.error(f"Error retrieving course memory: {e}")
            return None
    
    def update_course_memory(self, course_type: str, conversation_messages: List[Dict[str, str]]):
        """Update course memory based on conversation"""
        try:
            # Get existing memory
            existing_memory = self.get_course_memory(course_type)
            
            # Prepare conversation for analysis
            conversation_text = "\n".join([
                f"{msg.get('role', 'user')}: {msg.get('content', '')}" 
                for msg in conversation_messages[-5:]  # Last 5 messages
            ])
            
            system_prompt = f"""Analyze this conversation about {course_type} course and extract insights:

Current course memory: {existing_memory.model_dump() if existing_memory else "No existing memory"}

Conversation:
{conversation_text}

Extract:
- common_questions: New questions students ask about this course
- success_stories: Any positive outcomes or testimonials mentioned
- prerequisites: Educational background requirements mentioned
- difficulty_insights: How challenging students find this course
- enrollment_patterns: When students typically want to start

Return ONLY a JSON object with new insights to add."""

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content="Extract course insights from this conversation.")
            ]
            
            response = self.llm.invoke(messages)
            
            # Parse and update course memory
            try:
                response_text = response.content.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:-3]
                elif response_text.startswith('```'):
                    response_text = response_text[3:-3]
                
                insights = json.loads(response_text)
                
                # Merge with existing memory
                if existing_memory:
                    memory_data = existing_memory.model_dump()
                    # Append to lists instead of replacing
                    for key, value in insights.items():
                        if isinstance(value, list) and key in memory_data:
                            memory_data[key].extend(value)
                            # Remove duplicates
                            memory_data[key] = list(set(memory_data[key]))
                        elif value:  # Only update if value is not empty
                            memory_data[key] = value
                else:
                    memory_data = insights
                    memory_data['course_type'] = course_type
                
                memory_data['last_updated'] = datetime.now()
                updated_memory = CourseMemory(**memory_data)
                
                # Save to database
                self.course_memory_collection.update_one(
                    {"course_type": course_type},
                    {"$set": updated_memory.model_dump()},
                    upsert=True
                )
                
                logger.info(f"✅ Updated course memory for {course_type}")
                
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing course memory response: {e}")
                
        except Exception as e:
            logger.error(f"Error updating course memory: {e}")
    
    def save_selected_product(self, user_id: str, product_name: str, product_code: str) -> None:
        """Save the selected product to user profile"""
        try:
            profile = self.get_student_profile(user_id) or StudentProfile()

            # Update selected product
            profile.selected_product = {
                "name": product_name,
                "code": product_code
            }
            profile.last_updated = datetime.now()

            # Save to database
            self.profiles_collection.update_one(
                {"user_id": user_id},
                {"$set": {**profile.to_dict(), "user_id": user_id}},
                upsert=True
            )

            logger.info(f"✅ Saved selected product for user {user_id}: {product_name} ({product_code})")

        except Exception as e:
            logger.error(f"Error saving selected product: {e}")

    def get_selected_product(self, user_id: str) -> Optional[Dict[str, str]]:
        """Get the user's selected product"""
        try:
            profile = self.get_student_profile(user_id)
            if profile and profile.has_selected_product():
                return profile.selected_product
            return None
        except Exception as e:
            logger.error(f"Error getting selected product: {e}")
            return None

    def clear_selected_product(self, user_id: str) -> None:
        """Clear the selected product from user profile"""
        try:
            profile = self.get_student_profile(user_id)
            if profile:
                profile.selected_product = None
                profile.last_updated = datetime.now()

                # Save to database
                self.profiles_collection.update_one(
                    {"user_id": user_id},
                    {"$set": {**profile.to_dict(), "user_id": user_id}},
                    upsert=True
                )

                logger.info(f"✅ Cleared selected product for user {user_id}")

        except Exception as e:
            logger.error(f"Error clearing selected product: {e}")

    def get_personalized_context(self, user_id: str, _query: str = None) -> str:
        """Get personalized context for the user's query"""
        try:
            profile = self.get_student_profile(user_id)

            if not profile:
                return "🆕 NEW CUSTOMER: No student profile available. This is a new customer - provide detailed welcome message and collect name, email, and phone number."

            # Build context based on profile
            context_parts = []

            # Determine if this is a new or returning customer
            has_basic_info = profile.name or profile.email or profile.phone
            is_complete_profile = profile.name and profile.email and profile.phone

            if is_complete_profile:
                context_parts.append(f"🔄 RETURNING CUSTOMER: {profile.name}")
                context_parts.append("This customer has been here before - use their name and reference their history.")
            elif has_basic_info:
                context_parts.append("🔄 PARTIAL RETURNING CUSTOMER: Some information available")
                context_parts.append("This customer has started the process before - continue where they left off.")
            else:
                context_parts.append("🆕 NEW CUSTOMER: No previous information")
                context_parts.append("This is a new customer - provide detailed welcome message.")

            # Contact information - be explicit about what's available
            contact_info = []
            if profile.name:
                contact_info.append(f"Name: {profile.name}")
            if profile.email:
                contact_info.append(f"Email: {profile.email}")
            if profile.phone:
                contact_info.append(f"Phone: {profile.phone}")

            if contact_info:
                context_parts.append("\nSTUDENT INFORMATION AVAILABLE (you can share this with the user when asked):")
                context_parts.extend(contact_info)

                # Check if all required info is available
                if profile.name and profile.email and profile.phone:
                    context_parts.append("✅ ALL REQUIRED CONTACT INFORMATION IS AVAILABLE - DO NOT ASK FOR NAME, EMAIL, OR PHONE")
                    context_parts.append("✅ You can freely share this information when the user asks about their profile or details")
                else:
                    missing = []
                    if not profile.name:
                        missing.append("name")
                    if not profile.email:
                        missing.append("email")
                    if not profile.phone:
                        missing.append("phone")
                    context_parts.append(f"❌ MISSING: {', '.join(missing)} - only ask for these missing items")
            else:
                context_parts.append("❌ NO CONTACT INFORMATION AVAILABLE - need to collect name, email, and phone")

            # Other profile information
            if profile.education_level:
                context_parts.append(f"Education Level: {profile.education_level}")

            if profile.course_interests:
                context_parts.append(f"Previous Interests: {', '.join(profile.course_interests)}")

            if profile.preferred_language:
                context_parts.append(f"Preferred Language: {profile.preferred_language}")

            if profile.learning_goals:
                context_parts.append(f"Learning Goals: {profile.learning_goals}")

            # Include selected product if available
            if profile.has_selected_product():
                context_parts.append(f"Previously Selected Course: {profile.selected_product['name']} ({profile.selected_product['code']})")

            # Add instruction about sharing information
            if contact_info:
                context_parts.append("\nIMPORTANT: When the user asks about their information, profile, or details, you can and should share the information listed above. This is not personal/private information that needs to be protected - it's their own information that they provided and you're helping them access.")

            return "\n".join(context_parts) if context_parts else "Basic profile available."

        except Exception as e:
            logger.error(f"Error getting personalized context: {e}")
            return "Error retrieving student context."


# Global memory system instances (will be initialized per tenant)
_memory_systems: Dict[str, MemorySystem] = {}


def get_memory_system(tenant_id: str, llm_provider: str = "gemini") -> MemorySystem:
    """Get or create memory system for tenant"""
    key = f"{tenant_id}_{llm_provider}"
    if key not in _memory_systems:
        _memory_systems[key] = MemorySystem(tenant_id, llm_provider)
    return _memory_systems[key]
